import cv2 # Added for image saving
import numpy as np # Added for image processing
import base64 # Added for base64 encoding of image bytes
from PIL import Image
import io
from fastapi import UploadFile
from docnate.utils.settings import LayoutType
from docnate.datapoint.box import iou # Import iou function
from typing import Optional
from pdf2image import convert_from_bytes
import pillow_heif
from docx import Document # This might require `python-docx` package


def _get_image_array_from_file_data(file_data: bytes, file_extension: str) -> Optional[np.ndarray]:
    """Convert various file types to a numpy image array in memory."""
    img_array = None

    try:
        if file_extension == ".pdf":
            if not file_data:
                print(f"Warning: PDF file data is empty for conversion.")
                return None
            images = convert_from_bytes(file_data, dpi=200)
            if images:
                img_array = np.array(images[0]) # Take the first page
                
        elif file_extension == ".docx":
            doc_stream = io.BytesIO(file_data)
            doc = Document(doc_stream)
            for rel in doc.part.rels.values():
                if "image" in rel.target_ref:
                    image_data = rel.target_part.blob
                    image = Image.open(io.BytesIO(image_data))
                    img_array = np.array(image)
                    break # Take the first embedded image
        elif file_extension == ".heic":
            heif_file = pillow_heif.open_heif(io.BytesIO(file_data))
            image = heif_file.to_pillow()
            img_array = np.array(image)
        else:
            image = Image.open(io.BytesIO(file_data))
            img_array = np.array(image)

    except Exception as e:
        print(f"Error converting file of type {file_extension}: {str(e)}")
        return None

    return img_array


async def get_annotated_image_response_data(page, original_filename, original_file_type, original_image_path, file: UploadFile):
    """
    Generates an annotated image from a docnate page object or an original image file,
    converts it to PNG bytes, base64 encodes it, and returns a dictionary
    for the API response.
    """
    if file:
        file_data = await file.read()
        img_array = _get_image_array_from_file_data(file_data, original_file_type)
    
    if img_array is None:
        # Fallback to page.image if original_image_path is not provided or file not found
        if isinstance(page.image, np.ndarray):
            img_array = page.image
        elif isinstance(page.image, Image.Image):
            img_array = np.array(page.image)
        elif hasattr(page.image, 'get_array'): # Assume it's a docnate.datapoint.image.Image object
            img_array = page.image.get_array()
    
    if img_array is None:
        print(f"No raw image array found for page in {original_filename}.")
        return None

    # Ensure img_array is in RGB format if it's a 3-channel numpy array
    if isinstance(img_array, np.ndarray) and img_array.ndim == 3 and img_array.shape[2] == 3:
        img_array = cv2.cvtColor(img_array, cv2.COLOR_BGR2RGB)

    # Convert RGB to BGR for OpenCV for drawing
    annotated_img = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

    # Define colors for annotations (BGR format)
    COLOR_MAP = {
        "card": (255, 0, 0),       # Blue
        "card_number": (0, 255, 0), # Green
        "no_card": (0, 0, 255),     # Red
    }

    for layout in page.layouts:
        category = layout.category_name.value
        
        # Only draw annotations for specified categories
        if category in COLOR_MAP:
            bbox = layout.bounding_box.to_list(mode='xyxy') # [x1, y1, x2, y2]
            score = layout.score

            x1, y1, x2, y2 = map(int, bbox)
            color = COLOR_MAP.get(category) # Get color for the category
            
            # Draw rectangle
            cv2.rectangle(annotated_img, (x1, y1), (x2, y2), color, 2) # 2 is thickness

            # Add text label
            label = f"{category}: {score:.2f}" if score else category
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.7
            font_thickness = 2
            text_size = cv2.getTextSize(label, font, font_scale, font_thickness)[0]
            
            # Position text above the bounding box
            text_x = x1
            text_y = y1 - 10 if y1 - 10 > text_size[1] else y1 + text_size[1] + 10 # Avoid going off-image top

            cv2.putText(annotated_img, label, (text_x, text_y), font, font_scale, color, font_thickness, cv2.LINE_AA)

    # Convert annotated image (BGR numpy array) to RGB for PIL, then to bytes
    annotated_img_rgb = cv2.cvtColor(annotated_img, cv2.COLOR_BGR2RGB)
    pil_img = Image.fromarray(annotated_img_rgb)
    img_byte_arr = io.BytesIO()
    pil_img.save(img_byte_arr, format="PNG")
    img_bytes = img_byte_arr.getvalue()

    return {
        "Image_str": base64.b64encode(img_bytes).decode('utf-8'),
        "filename": original_filename,
        "file_type": original_file_type
    }


def is_card_number(text: str) -> bool:
    """Check if text looks like a card number"""
    if '/' in text:  # quick filter for expiry dates
        return False
    return text.isdigit() and len(text) > 3


async def get_masked_image_response_data(page, original_filename, original_file_type, original_image_path, file: UploadFile):
    """
    Generates a masked image from a DeepDoctection page object or an original image file,
    converts it to PNG bytes, base64 encodes it, and returns a dictionary
    for the API response. This function applies masking to both model-detected
    card numbers and OCR-detected potential card numbers.
    """
    
    if file:
        file_data = await file.read()
        img_array = _get_image_array_from_file_data(file_data, original_file_type)
    
    if img_array is None:
        # Fallback to page.image if original_image_path is not provided or file not found
        if isinstance(page.image, np.ndarray):
            img_array = page.image
        elif isinstance(page.image, Image.Image):
            img_array = np.array(page.image)
        elif hasattr(page.image, 'get_array'): # Assume it's a docnate.datapoint.image.Image object
            img_array = page.image.get_array()
    
    if img_array is None:
        print(f"No raw image array found for page in {original_filename}.")
        return None

    # Ensure img_array is in RGB format if it's a 3-channel numpy array
    if isinstance(img_array, np.ndarray) and img_array.ndim == 3 and img_array.shape[2] == 3:
        img_array = cv2.cvtColor(img_array, cv2.COLOR_BGR2RGB)

    # Convert RGB to BGR for OpenCV for drawing
    masked_img = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

    # Define color for masking (black)
    MASK_COLOR = (0, 0, 0) # Black in BGR
    
    masked_cards_details = [] # To store details of each masked card

    card_layouts = [layout for layout in page.layouts if layout.category_name.value == "card"]
    card_number_layouts = [layout for layout in page.layouts if layout.category_name.value == "card_number"]

    # Keep track of card layouts that have a corresponding card_number layout
    matched_card_layouts_indices = set()

    # Mask model-detected card numbers first
    for layout in card_number_layouts:
        bbox = layout.bounding_box.to_list(mode='xyxy') # [x1, y1, x2, y2]
        x1, y1, x2, y2 = map(int, bbox)
        cv2.rectangle(masked_img, (x1, y1), (x2, y2), MASK_COLOR, -1) # -1 is for filled rectangle
        masked_cards_details.append({"service": "YOLO"})

        
        num_x1, num_y1, num_x2, num_y2 = map(int, layout.bounding_box.to_list(mode='xyxy')) # Get card_number bbox once

        for idx, card_layout in enumerate(card_layouts):
            card_x1, card_y1, card_x2, card_y2 = map(int, card_layout.bounding_box.to_list(mode='xyxy'))

            # Check for full containment: card_number_layout must be entirely within card_layout
            if (num_x1 >= card_x1 and num_y1 >= card_y1 and
                num_x2 <= card_x2 and num_y2 <= card_y2):
                matched_card_layouts_indices.add(idx)
                break

    # Process unmatched 'card' layouts with OCR
    ocr_word_annotations = page.get_annotation(category_names=LayoutType.WORD)
    
    for idx, card_layout in enumerate(card_layouts):
        if idx not in matched_card_layouts_indices:
            # This card layout does not have a YOLO-detected card_number, use OCR
            card_bbox = card_layout.bounding_box.to_list(mode='xyxy')
            card_x1, card_y1, card_x2, card_y2 = map(int, card_bbox)
            
            ocr_masked_for_this_card = False

            if ocr_word_annotations:
                for word_ann in ocr_word_annotations:
                    if word_ann.characters and word_ann.bounding_box:
                        word_bbox = word_ann.bounding_box.to_list(mode="xyxy")
                        word_x1, word_y1, word_x2, word_y2 = map(int, word_bbox)

                        # Check if OCR word is within the current unmatched card layout
                        if (word_x1 >= card_x1 and word_y1 >= card_y1 and
                            word_x2 <= card_x2 and word_y2 <= card_y2):
                            
                            text = word_ann.characters
                            if is_card_number(text):
                                cv2.rectangle(masked_img, (word_x1, word_y1), (word_x2, word_y2), MASK_COLOR, -1)
                                if not ocr_masked_for_this_card:
                                    masked_cards_details.append({"service": "OCR"}) # Log once per card layout
                                    ocr_masked_for_this_card = True

    # Convert masked image (BGR numpy array) to RGB for PIL, then to bytes
    masked_img_rgb = cv2.cvtColor(masked_img, cv2.COLOR_BGR2RGB)
    pil_img = Image.fromarray(masked_img_rgb)
    img_byte_arr = io.BytesIO()
    pil_img.save(img_byte_arr, format="PNG")
    img_bytes = img_byte_arr.getvalue()

    return {
        "Image_str": base64.b64encode(img_bytes).decode('utf-8'),
        "filename": original_filename,
        "file_type": original_file_type,
        "masked_cards_details": masked_cards_details # Return list of masked card details
    }
