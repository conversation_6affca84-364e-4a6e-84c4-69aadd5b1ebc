from fastapi import UploadFile, HTTPException
from docx import Document
from PIL import Image
import io
import shutil
import os
from pathlib import Path
import uuid # Added for generating unique directory names

async def handle_file_processing(file: UploadFile, file_extension: str, file_data: bytes):
    file_to_analyze_path = None
    cleanup_paths = []  # Changed to a list
    original_file_path_for_page = None
    temp_upload_base_dir = Path("Services/equifax_service/temp_uploaded_files")
    temp_upload_base_dir.mkdir(parents=True, exist_ok=True)
    cleanup_paths.append(temp_upload_base_dir) # Add base dir to cleanup list

    try:
        if file_extension == ".docx":
            file_bytes = await file.read()
            temp_dir_path = Path("Services/equifax_service/docx_images")
            temp_dir_path.mkdir(parents=True, exist_ok=True)
            
            doc_stream = io.BytesIO(file_bytes)
            doc = Document(doc_stream)
            
            image_count = 0
            for rel in doc.part.rels.values():
                if "image" in rel.target_ref:
                    image_data = rel.target_part.blob
                    image = Image.open(io.BytesIO(image_data))
                    
                    image_path = temp_dir_path / f"image_{image_count}.png"
                    image.save(image_path)
                    image_count += 1
            
            if image_count == 0:
                print(f"No images found in DOCX file {file.filename}. Proceeding with docnate on the empty directory.")
            
            file_to_analyze_path = temp_dir_path
            cleanup_paths.append(temp_dir_path) # Add to cleanup list
            # For DOCX, original_file_path_for_page will be handled in Equifax_redaction.py loop
            
        else:
            image_extensions = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp", ".heic"}
            
            if file_extension in image_extensions:
                unique_dir_name = str(uuid.uuid4())
                temp_image_dir = temp_upload_base_dir / unique_dir_name
                temp_image_dir.mkdir(parents=True, exist_ok=True)
                
                temp_file_path = temp_image_dir / file.filename
                with open(temp_file_path, "wb") as buffer:
                    buffer.write(file_data)
                
                file_to_analyze_path = temp_image_dir
                cleanup_paths.append(temp_image_dir) # Add to cleanup list
                original_file_path_for_page = temp_file_path
                
            else:
                temp_file_path = temp_upload_base_dir / file.filename
                with open(temp_file_path, "wb") as buffer:
                    buffer.write(file_data)
                
                file_to_analyze_path = temp_file_path
                cleanup_paths.append(temp_file_path) # Add to cleanup list
                # For PDFs, original_file_path_for_page will remain None, so it will fall back to page.image.get_array()
        
        return {
            "file_to_analyze_path": file_to_analyze_path,
            "cleanup_paths": cleanup_paths, # Return the list
            "original_file_path_for_page": original_file_path_for_page,
            "file_extension": file_extension,
            "filename": file.filename
        }
            
    except Exception as e:
        # Clean up temporary files/directories in case of an error during file handling
        for path_to_clean in cleanup_paths: # Iterate through the list
            if path_to_clean.is_dir():
                shutil.rmtree(path_to_clean)
            elif path_to_clean.is_file():
                os.remove(path_to_clean)
        # The base directory cleanup is now handled by being in cleanup_paths
        raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")
