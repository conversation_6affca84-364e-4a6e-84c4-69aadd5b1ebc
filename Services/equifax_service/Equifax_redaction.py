from fastapi import FastAP<PERSON>, UploadFile, File, HTTPException
#from Services.equifax_service.pipeline import docnate_pipeline
from Services.equifax_service.doctr_pipeline import docnate_pipeline
from pathlib import Path
from Services.equifax_service.annotation import get_annotated_image_response_data,get_masked_image_response_data
from Services.equifax_service.filehandler import handle_file_processing
import shutil
import os

from .logger import app_logger

app = FastAPI()

@app.post("/wrapper/upload")
async def upload_file(file: UploadFile = File(...)):
    file_extension = Path(file.filename).suffix.lower()
    model_name = "/home/<USER>/python ml/equifax-card_redaction/weights/best.pt"
    analyzer = docnate_pipeline(model_name=model_name)
    
    result = await handle_file_processing(file, file_extension)
    
    file_to_analyze_path = result["file_to_analyze_path"]
    cleanup_paths = result["cleanup_paths"] # Changed to cleanup_paths
    original_file_path_for_page = result["original_file_path_for_page"]
    filename = result["filename"]

    annotated_images_data = []
    try:
        df_result = analyzer.analyze(path=str(file_to_analyze_path))
        df_result.reset_state()

        if file_extension == ".docx":
            # For DOCX, original_image_path_for_page needs to be constructed in the loop
            for idx, dp in enumerate(df_result):
                original_image_path_for_page_docx = file_to_analyze_path / f"image_{idx}.png"
                img_data = await get_masked_image_response_data(dp, filename, file_extension, original_image_path=original_image_path_for_page_docx,file=file)
                if img_data:
                    annotated_images_data.append(img_data)
                
                masked_cards_details = img_data.get("masked_cards_details", [])

                if masked_cards_details:
                    for card_detail in masked_cards_details:
                        app_logger.log_api_call(
                            filename=filename,
                            file_type=file_extension,
                            is_card="Yes",
                            status="Masked",
                            api=card_detail["service"],
                            page_no=idx
                        )
                else:
                    app_logger.log_api_call(
                        filename=filename,
                        file_type=file_extension,
                        is_card="No",
                        status="No_Card",
                        api="No_Masking",
                        page_no=idx
                    )
        else:
            for idx, dp in enumerate(df_result): # Added idx for page_no
                img_data = await get_masked_image_response_data(dp, filename, file_extension, original_image_path=original_file_path_for_page,file=file)
                if img_data:
                    annotated_images_data.append(img_data)

                masked_cards_details = img_data.get("masked_cards_details", [])

                if masked_cards_details:
                    for card_detail in masked_cards_details:
                        app_logger.log_api_call(
                            filename=filename,
                            file_type=file_extension,
                            is_card="Yes",
                            status="Masked",
                            api=card_detail["service"],
                            page_no=idx
                        )
                else:
                    app_logger.log_api_call(
                        filename=filename,
                        file_type=file_extension,
                        is_card="No",
                        status="No_Card",
                        api="No_Masking",
                        page_no=idx
                    )
        
        return annotated_images_data

    except Exception as e:
        app_logger.log_error(f"Error in upload_file endpoint for {file.filename}", e)
        raise HTTPException(status_code=500, detail=f"Error processing file with docnate: {str(e)}")
    finally:
        # Clean up temporary files/directories
        for path_to_clean in cleanup_paths: # Iterate through the list
            if path_to_clean.is_dir():
                shutil.rmtree(path_to_clean)
            elif path_to_clean.is_file():
                os.remove(path_to_clean)
        
        # The base directory cleanup is now handled by being in cleanup_paths
        # No need for separate temp_upload_base_dir cleanup here as it's part of cleanup_paths
