import requests
import os
import json
import base64

url = 'http://localhost:8000/wrapper/upload'
file_path = '/home/<USER>/Downloads/finaldatawithheic/finaldata/f1two_card (214).heic' 

if not os.path.exists(file_path):
    print(f"Error: File '{file_path}' not found.")
    exit()

print(f"Attempting to upload: {file_path}")

all_responses = []

try:
    with open(file_path, 'rb') as f:
        files = {'file': (os.path.basename(file_path), f)}
        response = requests.post(url, files=files)

        print(f"Response for {file_path}:")
        print(f"Status Code: {response.status_code}")
        print(f"Response Body: {response.text}")

        try:
            response_json = response.json()
            all_responses.append({os.path.basename(file_path): response_json})
            print(f"Response for {file_path} added to list.")

            if 'img-str' in response_json and 'file_name' in response_json:
                try:
                    img_data = base64.b64decode(response_json['img-str'])
                    output_filename = response_json['file_name']
                    os.makedirs('masked_images', exist_ok=True)
                    save_path = os.path.join('masked_images', output_filename)
                    with open(save_path, 'wb') as img_file:
                        img_file.write(img_data)
                    print(f"Encoded image saved to {save_path}")
                except Exception as img_e:
                    print(f"Error saving encoded image for {file_path}: {img_e}")
            else:
                print(f"No 'img-str' or 'file_name' found in response for {file_path}.")

        except json.JSONDecodeError:
            print(f"Could not decode JSON response for {file_path}. Saving raw text.")
            all_responses.append({os.path.basename(file_path): response.text})
        
        print("-" * 30)

except FileNotFoundError:
    print(f"Error: File not found at {file_path}.")
except requests.exceptions.ConnectionError:
    print(f"Error: Could not connect to the server at {url}. Please ensure the server is running.")
except Exception as e:
    print(f"An unexpected error occurred while uploading {file_path}: {e}")

if all_responses:
    try:
        with open('result.json', 'w') as f:
            json.dump(all_responses, f, indent=4)
        print("\nResponse saved to result.json")
    except Exception as e:
        print(f"Error saving response to result.json: {e}")
else:
    print("\nNo response to save to result.json.")
