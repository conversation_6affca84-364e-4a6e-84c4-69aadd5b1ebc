import requests
import os
import json # Import json module at the top
import base64 # Import base64 module

# API endpoint
url = 'http://127.0.0.1:8000/wrapper/upload'

# Path to the Data folder
data_folder = 'Services/equifax_service/testing/Data'

# Get a list of all files in the Data folder
try:
    files_to_upload = [f for f in os.listdir(data_folder) if os.path.isfile(os.path.join(data_folder, f))]
except FileNotFoundError:
    print(f"Error: The folder '{data_folder}' was not found.")
    exit()
except Exception as e:
    print(f"An error occurred while listing files: {e}")
    exit()

if not files_to_upload:
    print(f"No files found in the '{data_folder}' folder to upload.")
    exit()

all_responses = []

for filename in files_to_upload:
    filepath = os.path.join(data_folder, filename)
    print(f"Attempting to upload: {filepath}")

    try:
        with open(filepath, 'rb') as f:
            files = {'file': (filename, f)}
            response = requests.post(url, files=files)

            print(f"Response for {filename}:")
            print(f"Status Code: {response.status_code}")
            print(f"Response Body: {response.text}")

            try:
                response_json = response.json()
                all_responses.append({filename: response_json})
                print(f"Response for {filename} added to list.")

                # The response is now a list of JSON objects
                if isinstance(response_json, list):
                    for i, img_data_json in enumerate(response_json):
                        # Check if 'Image_str' and 'filename' exist in each item of the response list
                        if 'Image_str' in img_data_json and 'filename' in img_data_json:
                            try:
                                img_data = base64.b64decode(img_data_json['Image_str'])
                                output_filename = img_data_json['filename']
                                # Append index to filename to avoid overwriting if multiple images have the same name
                                name, ext = os.path.splitext(output_filename)
                                output_filename_indexed = f"{name}_{i}.png"
                                os.makedirs('Services/equifax_service/testing/masked_images', exist_ok=True)
                                save_path = os.path.join('Services/equifax_service/testing/masked_images', output_filename_indexed)
                                with open(save_path, 'wb') as img_file:
                                    img_file.write(img_data)
                                print(f"Encoded image saved to {save_path}")
                            except Exception as img_e:
                                print(f"Error saving encoded image for {filename} (item {i}): {img_e}")
                        else:
                            print(f"No 'Image_str' or 'filename' found in response item {i} for {filename}.")
                else:
                    print(f"Unexpected response format for {filename}. Expected a list of JSON objects.")

            except json.JSONDecodeError:
                print(f"Could not decode JSON response for {filename}. Saving raw text.")
                all_responses.append({filename: response.text})
            
            print("-" * 30)

    except FileNotFoundError:
        print(f"Error: File not found at {filepath}. Skipping.")
    except requests.exceptions.ConnectionError:
        print(f"Error: Could not connect to the server at {url}. Please ensure the server is running.")
        break # Exit if connection fails for one file, likely to fail for others
    except Exception as e:
        print(f"An unexpected error occurred while uploading {filename}: {e}")

# Save all responses to result.json as a single JSON array
if all_responses:
    try:
        with open('Services/equifax_service/testing/result.json', 'w') as f:
            json.dump(all_responses, f, indent=4)
        print("\nAll responses saved to result.json")
    except Exception as e:
        print(f"Error saving responses to result.json: {e}")
else:
    print("\nNo responses to save to result.json.")
